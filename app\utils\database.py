from sqlalchemy.ext.declarative import declarative_base
from contextlib import contextmanager
import logging
import os
import sys
from app.core.models.config import engine, SessionLocal, BaseDBModel
from app.utils.toml_parser import load_from_file
from app.core.models import User, UserStatus
from passlib.context import CryptContext

# Importer tous les modèles pour s'assurer qu'ils sont chargés
import app.core.models

# Déterminer le chemin du fichier de configuration
if getattr(sys, 'frozen', False):
    # Si l'application est compilée avec PyInstaller
    base_dir = sys._MEIPASS
    config_path = os.path.join(base_dir, 'config', 'settings.toml')
else:
    # Si l'application est exécutée normalement
    config_path = "config/settings.toml"

# Chargement de la configuration
try:
    config = load_from_file(config_path)
except Exception as e:
    logging.error(f"Erreur lors du chargement de la configuration: {str(e)}")
    config = {}

logger = logging.getLogger(__name__)

@contextmanager
def get_db():
    """Gestionnaire de contexte pour les sessions de base de données."""
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        db.rollback()
        logger.error(f"Erreur de base de données: {str(e)}")
        raise
    finally:
        db.close()

def init_db():
    """Initialise la base de données et garantit l'accès admin par défaut."""
    try:
        # Importer tous les modèles dans le bon ordre pour éviter les problèmes de relations
        from app.core.models.base import TimestampMixin
        from app.core.models.permission import Permission, DBRole
        from app.core.models.user import User
        from app.core.models.user_role import UserRole
        from app.core.models.audit import AuditLog
        from app.core.models.inventory import InventoryItem, InventoryMovement
        from app.core.models.repair import RepairOrder, RepairPayment
        from app.core.models.maintenance import MaintenanceSchedule
        from app.core.models.supplier import Supplier
        from app.core.models.customer import Customer
        from app.core.models.notification import Notification
        from app.core.models.purchasing import PurchaseOrder, PurchaseOrderItem
        from app.core.models.sale import Sale, SaleItem, Payment, Quote, QuoteItem, CustomerLoyalty
        from app.core.models.treasury import CashRegister, CashTransaction, Expense
        from app.core.models.supplier_finance import SupplierPayment, SupplierInvoice

        # Configurer les mappers après tous les imports
        from sqlalchemy.orm import configure_mappers
        configure_mappers()

        # Créer les tables si elles n'existent pas
        BaseDBModel.metadata.create_all(bind=engine)
    except Exception as e:
        logger.error(f"Erreur lors de l'initialisation de la base de données: {e}")
        raise

    db = SessionLocal()
    try:
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        hashed_password = pwd_context.hash("admin1234")
        admin_email = "<EMAIL>"
        # Supprimer tout utilisateur admin existant
        db.query(User).filter_by(email=admin_email).delete()
        db.commit()
        # Créer l'utilisateur admin
        admin_user = User(
            email=admin_email,
            hashed_password=hashed_password,
            full_name="Administrateur",
            status=UserStatus.ACTIVE,
            is_active=True
        )
        db.add(admin_user)
        db.commit()
        logger.info(f"Utilisateur admin créé: <EMAIL> / admin1234 (hash: {hashed_password})")
    except Exception as e:
        db.rollback()
        logger.error(f"Erreur lors de l'initialisation de la base de données: {str(e)}")
    finally:
        db.close()
Base = declarative_base()
def get_database_url():
    """Returns the database URL from environment or default"""
    import os
    from pathlib import Path

    # Get the base directory of the project
    base_dir = Path(__file__).resolve().parent.parent.parent

    # Default database path
    default_db_path = os.path.join(base_dir, "data", "maintenance.db")

    # Create data directory if it doesn't exist
    os.makedirs(os.path.dirname(default_db_path), exist_ok=True)

    # Return the database URL
    return f"sqlite:///{default_db_path}"
