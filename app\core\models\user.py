from datetime import datetime
from enum import Enum
from typing import Optional, List, TYPE_CHECKING, Dict, Any
from sqlalchemy import <PERSON>um<PERSON>, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship, Mapped, mapped_column
from app.core.models.base import BaseDBModel, TimestampMixin, BaseModelTimestamp
from pydantic import BaseModel, EmailStr, Field

if TYPE_CHECKING:
    from app.core.models.inventory import InventoryMovement
    from app.core.models.maintenance import MaintenanceSchedule
    from app.core.models.user_role import UserRole
    from app.core.models.permission import DBRole
    from app.core.models.audit import AuditLog

class UserStatus(str, Enum):
    """Statut de l'utilisateur"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING = "pending"

class User(BaseDBModel, TimestampMixin):
    __tablename__ = "users"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    email: Mapped[str] = mapped_column(String, unique=True, index=True)
    hashed_password: Mapped[str] = mapped_column(String)
    full_name: Mapped[str] = mapped_column(String)
    status: Mapped[UserStatus] = mapped_column(String, default=UserStatus.ACTIVE)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    last_login: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    phone: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    position: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    department: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    profile_image: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    two_factor_enabled: Mapped[bool] = mapped_column(Boolean, default=False)
    totp_secret: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    password_reset_token: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    password_reset_expires: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    failed_login_attempts: Mapped[int] = mapped_column(Integer, default=0)
    last_failed_login: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    preferences: Mapped[Optional[Dict[str, Any]]] = mapped_column(Text, nullable=True)

    # Relations
    roles = relationship("UserRole", back_populates="user")

# Modèle Pydantic pour la validation des données
class UserPydantic(BaseModelTimestamp):
    id: int
    email: EmailStr
    full_name: str
    status: UserStatus
    is_active: bool = True
    last_login: Optional[datetime] = None
    phone: Optional[str] = None
    position: Optional[str] = None
    department: Optional[str] = None
    profile_image: Optional[str] = None
    two_factor_enabled: bool = False
    roles: List["UserRolePydantic"] = []

class UserCreate(BaseModel):
    """Modèle Pydantic pour la création d'un utilisateur"""
    email: EmailStr
    password: str = Field(..., min_length=8)
    full_name: str
    phone: Optional[str] = None
    position: Optional[str] = None
    department: Optional[str] = None
    role_ids: List[int] = []

class UserUpdate(BaseModel):
    """Modèle Pydantic pour la mise à jour d'un utilisateur"""
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    status: Optional[UserStatus] = None
    is_active: Optional[bool] = None
    phone: Optional[str] = None
    position: Optional[str] = None
    department: Optional[str] = None
    profile_image: Optional[str] = None
    two_factor_enabled: Optional[bool] = None
    role_ids: Optional[List[int]] = None

class PasswordChange(BaseModel):
    """Modèle Pydantic pour le changement de mot de passe"""
    current_password: str
    new_password: str = Field(..., min_length=8)
    confirm_password: str

class PasswordReset(BaseModel):
    """Modèle Pydantic pour la réinitialisation de mot de passe"""
    token: str
    new_password: str = Field(..., min_length=8)
    confirm_password: str

class PasswordResetRequest(BaseModel):
    """Modèle Pydantic pour la demande de réinitialisation de mot de passe"""
    email: EmailStr

# Import circulaire à la fin pour éviter les problèmes
from app.core.models.user_role import UserRolePydantic

