from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QFrame
)
from PyQt6.QtCore import Qt

from app.core.services.customer_service import CustomerService
from app.utils.database import SessionLocal

class CustomerInfoWidget(QWidget):
    """Onglet Informations: affiche les infos de base du client"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = SessionLocal()
        self.service = CustomerService(self.db)
        self.customer_id = None
        self._build_ui()

    def __del__(self):
        try:
            if hasattr(self, 'db') and self.db:
                self.db.close()
        except Exception:
            pass

    def _build_ui(self):
        """Configure l'interface utilisateur avec le style unifié"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(12)

        # Style unifié inspiré de l'onglet réparation
        frame_style = "QFrame { border: none; background: transparent; }"
        title_style = "color: #555; font-weight: 600; font-size: 12px;"
        value_style = "color: #222; font-size: 14px; font-weight: 700;"

        def make_item(title, value_label_attr, word_wrap=False):
            w = QWidget()
            h = QHBoxLayout(w)
            h.setContentsMargins(6, 6, 6, 6)
            h.setSpacing(6)
            t = QLabel(title)
            t.setStyleSheet(title_style)
            t.setFixedWidth(120)
            t.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            h.addWidget(t)
            val = QLabel("-")
            val.setStyleSheet(value_style)
            val.setWordWrap(word_wrap)
            val.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            h.addWidget(val, 1)
            setattr(self, value_label_attr, val)
            return w

        # Frame 1 - Informations générales (colonne gauche)
        frame1 = QFrame()
        frame1.setStyleSheet(frame_style)
        f1 = QVBoxLayout(frame1)
        f1.setContentsMargins(8, 8, 8, 8)
        f1.setSpacing(8)
        f1.addWidget(make_item("Nom", "name_label"))
        f1.addWidget(make_item("Contact", "contact_label"))
        f1.addWidget(make_item("Téléphone", "phone_label"))
        f1.addWidget(make_item("Email", "email_label"))

        # Frame 2 - Informations d'adresse (colonne droite)
        frame2 = QFrame()
        frame2.setStyleSheet(frame_style)
        f2 = QVBoxLayout(frame2)
        f2.setContentsMargins(8, 8, 8, 8)
        f2.setSpacing(8)
        f2.addWidget(make_item("Adresse", "address_label", word_wrap=True))
        f2.addWidget(make_item("Ville", "city_label"))
        f2.addWidget(make_item("Statut", "active_label"))

        # Disposer les 2 frames côte à côte
        row_layout = QHBoxLayout()
        row_layout.setSpacing(12)
        row_layout.addWidget(frame1, 1)
        row_layout.addWidget(frame2, 1)
        main_layout.addLayout(row_layout)

        # Ajouter un stretch pour éviter que les frames s'étirent trop
        main_layout.addStretch()

        self.clear()

    def set_customer(self, customer_id: int):
        self.customer_id = customer_id
        self.refresh()

    def clear(self):
        self.customer_id = None
        self.name_label.setText("-")
        self.contact_label.setText("-")
        self.phone_label.setText("-")
        self.email_label.setText("-")
        self.address_label.setText("-")
        self.city_label.setText("-")
        self.active_label.setText("-")

    def refresh(self):
        """Met à jour l'affichage avec les données du client"""
        if not self.customer_id:
            self.clear()
            return
        try:
            # service.get est async; mais on peut accéder via session directe
            customer = self.service.db.query(self.service.model).get(self.customer_id)
            if not customer:
                self.clear()
                return

            # Informations générales
            self.name_label.setText(customer.name or "-")
            self.contact_label.setText(customer.contact_person or "-")
            self.phone_label.setText(customer.phone or "-")
            self.email_label.setText(customer.email or "-")

            # Adresse complète
            address_parts = []
            if customer.address:
                address_parts.append(customer.address)
            if getattr(customer, 'commune', None):
                address_parts.append(getattr(customer, 'commune'))
            if getattr(customer, 'postal_code', None):
                address_parts.append(getattr(customer, 'postal_code'))

            self.address_label.setText(", ".join(address_parts) if address_parts else "-")

            # Ville
            city = getattr(customer, 'city', None) or "-"
            self.city_label.setText(city)

            # Statut avec style visuel
            is_active = getattr(customer, 'active', True)
            status_text = "✅ Actif" if is_active else "❌ Inactif"
            self.active_label.setText(status_text)

        except Exception as e:
            print(f"Erreur lors du chargement des informations client: {e}")
            self.clear()