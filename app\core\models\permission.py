# Dans app/core/models/permission.py
from enum import Enum
from typing import List, Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Boolean, ForeignKey, Table, DateTime
from sqlalchemy.orm import relationship
from app.utils.database import Base
from .base import BaseDBModel, TimestampMixin, BaseModelTimestamp

class RoleEnum(Enum):
    ADMIN = "admin"
    MANAGER = "manager"
    TECHNICIAN = "technician"
    USER = "user"

# Table d'association role-permission
role_permission = Table(
    'role_permission',
    BaseDBModel.metadata,
    Column('role_id', Integer, ForeignKey('roles.id'), primary_key=True),
    Column('permission_id', Integer, ForeignKey('permissions.id'), primary_key=True)
)

class PermissionCategory(str, Enum):
    """Catégories de permissions"""
    USER = "user"
    CUSTOMER = "customer"
    SUPPLIER = "supplier"
    INVENTORY = "inventory"
    REPAIR = "repair"
    MAINTENANCE = "maintenance"
    PURCHASING = "purchasing"
    REPORTING = "reporting"
    SYSTEM = "system"

class Permission(BaseDBModel):
    """Modèle pour les permissions"""
    __tablename__ = "permissions"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    code = Column(String, unique=True, index=True)
    name = Column(String)
    description = Column(String, nullable=True)
    category = Column(String)

    # Relations
    roles = relationship("DBRole", secondary=role_permission, back_populates="permissions")

class DBRole(BaseDBModel):
    """Modèle pour les rôles"""
    __tablename__ = "roles"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True)
    description = Column(String, nullable=True)
    is_system = Column(Boolean, default=False)
    parent_id = Column(Integer, ForeignKey('roles.id'), nullable=True)

    # Relations
    permissions = relationship("Permission", secondary=role_permission, back_populates="roles")
    users = relationship("UserRole", back_populates="role")

class PermissionPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les permissions"""
    id: int
    code: str
    name: str
    description: str | None = None
    category: str

