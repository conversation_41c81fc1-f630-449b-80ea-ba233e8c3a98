from datetime import datetime
from typing import Optional, TYPE_CHECKING
from sqlalchemy import <PERSON>umn, Integer, ForeignKey, DateTime
from sqlalchemy.orm import relationship
from pydantic import BaseModel

from .base import BaseDBModel, BaseModelTimestamp

if TYPE_CHECKING:
    from .user import User
    from .permission import DBRole

class UserRole(BaseDBModel):
    __tablename__ = "user_roles"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    role_id = Column(Integer, ForeignKey("roles.id"))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relations - utiliser des chaînes pour éviter les imports circulaires
    user = relationship("User", back_populates="roles")
    role = relationship("DBRole", back_populates="users")

class UserRolePydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les rôles d'utilisateur"""
    id: int
    user_id: int
    role_id: int
