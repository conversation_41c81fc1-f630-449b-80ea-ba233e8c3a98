from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QTableView, QHBoxLayout,
    QPushButton, QHeaderView, QMessageBox, QDialog
)
from PyQt6.QtCore import Qt, QAbstractTableModel, QModelIndex, QVariant
from PyQt6.QtGui import QIcon
import asyncio
from datetime import datetime
from typing import List, Optional

from app.core.services.supplier_finance_service import SupplierFinanceService
from app.core.models.supplier_finance import SupplierPayment
from app.utils.database import SessionLocal


class SupplierPaymentsTableModel(QAbstractTableModel):
    """Modèle de table pour les paiements fournisseurs"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.payments: List[SupplierPayment] = []
        self.headers = [
            "Date", "Montant", "Méthode", "Réf<PERSON>rence", "Facture", "Notes"
        ]

    def rowCount(self, parent=QModelIndex()):
        return len(self.payments)

    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if orientation == Qt.Orientation.Horizontal and role == Qt.ItemDataRole.DisplayRole:
            return self.headers[section]
        return QVariant()

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid() or index.row() >= len(self.payments):
            return QVariant()

        payment = self.payments[index.row()]
        column = index.column()

        if role == Qt.ItemDataRole.DisplayRole:
            if column == 0:  # Date
                return payment.payment_date.strftime("%d/%m/%Y") if payment.payment_date else "N/A"
            elif column == 1:  # Montant
                return f"{payment.amount:.2f} DA"
            elif column == 2:  # Méthode
                return payment.payment_method.value if payment.payment_method else "N/A"
            elif column == 3:  # Référence
                return payment.reference or "N/A"
            elif column == 4:  # Facture
                return payment.invoice.invoice_number if payment.invoice else "N/A"
            elif column == 5:  # Notes
                return payment.notes or "N/A"

        elif role == Qt.ItemDataRole.UserRole:
            return payment.id

        return QVariant()

    def update_payments(self, payments: List[SupplierPayment]):
        """Met à jour la liste des paiements"""
        self.beginResetModel()
        self.payments = payments
        self.endResetModel()


class SupplierPaymentsWidget(QWidget):
    """Widget des paiements fournisseur avec vraies données."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self._supplier_id = None

        # Services
        self.db = SessionLocal()
        self.service = SupplierFinanceService(self.db)

        self._setup_ui()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()

    def _setup_ui(self):
        layout = QVBoxLayout(self)

        # En-tête
        header_layout = QHBoxLayout()
        self.title_label = QLabel("Paiements fournisseur")
        self.title_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #1976D2;")
        header_layout.addWidget(self.title_label)

        header_layout.addStretch()

        # Bouton "Enregistrer un acompte"
        self.advance_payment_button = QPushButton("Enregistrer un acompte")
        self.advance_payment_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.advance_payment_button.clicked.connect(self.record_advance_payment)
        self.advance_payment_button.setEnabled(False)  # Désactivé par défaut
        header_layout.addWidget(self.advance_payment_button)

        # Bouton "Paiement de factures"
        self.invoice_payment_button = QPushButton("Paiement de factures")
        self.invoice_payment_button.setIcon(QIcon("app/ui/resources/icons/payment.svg"))
        self.invoice_payment_button.clicked.connect(self.pay_invoices)
        self.invoice_payment_button.setEnabled(False)  # Désactivé par défaut
        header_layout.addWidget(self.invoice_payment_button)

        # Bouton de rafraîchissement
        self.refresh_button = QPushButton("Rafraîchir")
        self.refresh_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        self.refresh_button.clicked.connect(self.refresh_data)
        header_layout.addWidget(self.refresh_button)

        layout.addLayout(header_layout)

        # Tableau des paiements
        self.table_view = QTableView()
        self.table_model = SupplierPaymentsTableModel()
        self.table_view.setModel(self.table_model)

        # Configuration du tableau
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table_view.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.table_view.setAlternatingRowColors(True)
        self.table_view.setSortingEnabled(True)

        # Ajuster les colonnes
        header = self.table_view.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        layout.addWidget(self.table_view)

        # Label d'information
        self.info_label = QLabel("Aucun fournisseur sélectionné")
        self.info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.info_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(self.info_label)

    def set_supplier_id(self, supplier_id: int):
        self._supplier_id = supplier_id
        if supplier_id:
            self.info_label.setText(f"Chargement des paiements...")
            # Activer les boutons de paiement
            self.advance_payment_button.setEnabled(True)
            self.invoice_payment_button.setEnabled(True)
            self.load_payments()
        else:
            self.clear()

    def clear(self):
        self._supplier_id = None
        self.table_model.update_payments([])
        self.info_label.setText("Aucun fournisseur sélectionné")
        # Désactiver les boutons de paiement
        self.advance_payment_button.setEnabled(False)
        self.invoice_payment_button.setEnabled(False)

    def refresh_data(self):
        """Rafraîchit les données"""
        if self._supplier_id:
            self.load_payments()

    def load_payments(self):
        """Charge les paiements du fournisseur"""
        if not self._supplier_id:
            return

        try:
            # Utiliser la boucle existante si elle est en cours, sinon exécuter directement
            try:
                loop = asyncio.get_running_loop()
                # Une boucle est déjà en cours: planifier la tâche sans bloquer
                loop.create_task(self._load_payments_async())
            except RuntimeError:
                # Aucune boucle en cours: exécuter de manière synchrone
                asyncio.run(self._load_payments_async())
        except Exception as e:
            print(f"Erreur lors du chargement des paiements: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des paiements: {str(e)}")

    async def _load_payments_async(self):
        """Charge les paiements de manière asynchrone"""
        try:
            # Récupérer les paiements du fournisseur
            payments = await self.service.get_supplier_payments(self._supplier_id)

            # Mettre à jour le modèle
            self.table_model.update_payments(payments)

            # Mettre à jour le label d'information
            if payments:
                total_amount = sum(p.amount for p in payments)
                self.info_label.setText(f"{len(payments)} paiement(s) - Total: {total_amount:.2f} DA")
            else:
                self.info_label.setText("Aucun paiement trouvé pour ce fournisseur")

        except Exception as e:
            print(f"Erreur lors du chargement des paiements: {e}")
            self.info_label.setText("Erreur lors du chargement des paiements")

    def record_advance_payment(self):
        """Ouvre la boîte de dialogue pour enregistrer un acompte"""
        if not self._supplier_id:
            return

        try:
            from app.ui.views.supplier.dialogs.supplier_payment_dialog import SupplierPaymentDialog

            dialog = SupplierPaymentDialog(
                parent=self,
                supplier_id=self._supplier_id,
                invoice_id=None  # Pas de facture spécifique pour un acompte
            )

            # Connecter le signal pour rafraîchir après enregistrement
            dialog.payment_saved.connect(self.refresh_data)

            if dialog.exec() == QDialog.DialogCode.Accepted:
                # Le rafraîchissement sera fait automatiquement via le signal
                pass

        except Exception as e:
            print(f"Erreur lors de l'ouverture de la boîte de dialogue d'acompte: {e}")
            QMessageBox.critical(self, "Erreur", f"Impossible d'ouvrir la boîte de dialogue: {str(e)}")

    def pay_invoices(self):
        """Ouvre la boîte de dialogue pour payer des factures"""
        if not self._supplier_id:
            return

        try:
            from app.ui.views.supplier.dialogs.supplier_payment_dialog import SupplierPaymentDialog

            dialog = SupplierPaymentDialog(
                parent=self,
                supplier_id=self._supplier_id,
                invoice_id=None  # Permettra de sélectionner une facture dans la boîte de dialogue
            )

            # Connecter le signal pour rafraîchir après enregistrement
            dialog.payment_saved.connect(self.refresh_data)

            if dialog.exec() == QDialog.DialogCode.Accepted:
                # Le rafraîchissement sera fait automatiquement via le signal
                pass

        except Exception as e:
            print(f"Erreur lors de l'ouverture de la boîte de dialogue de paiement: {e}")
            QMessageBox.critical(self, "Erreur", f"Impossible d'ouvrir la boîte de dialogue: {str(e)}")