"""
Configuration centrale pour SQLAlchemy.
Ce module définit la base de modèle et la session.
"""
from typing import Dict, Any, ClassVar, Type
from sqlalchemy import create_engine, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, scoped_session, declared_attr
from sqlalchemy.pool import StaticPool

from app.utils.config import get_db_path

# Créer la base de modèle
class _BaseDBModel:
    __abstract__ = True

    @declared_attr
    def __tablename__(cls) -> str:
        """Génère automatiquement le nom de la table à partir du nom de la classe"""
        return cls.__name__.lower() + 's'

    def to_dict(self) -> Dict[str, Any]:
        """Convertit le modèle en dictionnaire"""
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}

    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """Crée une instance à partir d'un dictionnaire"""
        return cls(**data)

BaseDBModel = declarative_base(cls=_BaseDBModel)

# Créer l'engine
db_path = get_db_path()
engine = create_engine(
    f"sqlite:///{db_path}",
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
    echo=False  # Mettre à True pour voir les requêtes SQL
)

# Créer la session
session_factory = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    expire_on_commit=False  # Empêche l'expiration automatique des objets après commit
)
SessionLocal = scoped_session(session_factory)

# Event listener pour mettre à jour updated_at automatiquement
@event.listens_for(BaseDBModel, 'before_update', propagate=True)
def timestamp_before_update(mapper, connection, target):
    """Met à jour le champ updated_at avant chaque mise à jour"""
    from datetime import datetime, timezone
    if hasattr(target, 'updated_at'):
        target.updated_at = datetime.now(timezone.utc)

# Event listener pour incrémenter la version
@event.listens_for(BaseDBModel, 'before_update', propagate=True)
def increment_version(mapper, connection, target):
    """Incrémente la version à chaque mise à jour"""
    if hasattr(target, 'version'):
        target.version += 1

def get_db():
    """Retourne une session de base de données"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_db():
    """Initialise la base de données et crée les tables"""
    # Import all models here to ensure they are registered with SQLAlchemy
    # pylint: disable=unused-import,import-outside-toplevel
    from app.core.models.base import TimestampMixin
    from app.core.models.permission import Permission, DBRole
    from app.core.models.user import User
    from app.core.models.user_role import UserRole
    from app.core.models.audit import AuditLog
    from app.core.models.inventory import InventoryItem, InventoryMovement
    from app.core.models.repair import RepairOrder
    from app.core.models.maintenance import MaintenanceSchedule
    from app.core.models.supplier import Supplier
    from app.core.models.customer import Customer
    from app.core.models.notification import Notification
    from app.core.models.purchasing import PurchaseOrder, PurchaseOrderItem
    from app.core.models.sale import Sale, SaleItem, Payment, Quote, QuoteItem, CustomerLoyalty

    # Créer toutes les tables
    BaseDBModel.metadata.create_all(bind=engine)

    return SessionLocal()

def reconfigure_engine(new_db_path: str):
    """Recrée l'engine et la session SQLAlchemy avec un nouveau chemin de base de données."""
    global engine, SessionLocal, session_factory
    from app.utils.config import set_db_path
    set_db_path(new_db_path)
    engine.dispose()
    engine = create_engine(
        f"sqlite:///{new_db_path}",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        echo=False
    )
    session_factory = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=engine,
        expire_on_commit=False
    )
    SessionLocal = scoped_session(session_factory)
