from datetime import datetime
from typing import Optional, List, TYPE_CHECKING
from enum import Enum
from pydantic import BaseModel, EmailStr
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Boolean, Enum as SQLEnum, JSON
from sqlalchemy.orm import relationship
from .base import BaseDBModel, TimestampMixin, BaseModelTimestamp
from .user_role import UserRole  # Import UserRole from user_role.py

if TYPE_CHECKING:
    from .user import User  # Import User for type checking only

# Import User at the end of the file to avoid circular imports

# Énumérations
class RoleType(str, Enum):
    ADMIN = "admin"
    MANAGER = "manager"
    TECHNICIAN = "technician"
    SALES = "sales"
    INVENTORY = "inventory"

# Le modèle Role a été déplacé vers app/core/models/permission.py (DBRole)
# pour éviter les conflits de table

# Modèles Pydantic pour la compatibilité (utiliser DBRole de permission.py à la place)
class RolePydantic(BaseModelTimestamp):
    id: int
    name: str  # Changé de RoleType à str pour compatibilité avec DBRole
    description: Optional[str] = None
    is_system: bool = False
    parent_id: Optional[int] = None

# Import User at the end to avoid circular imports
from .user import User