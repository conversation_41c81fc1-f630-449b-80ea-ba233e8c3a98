from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QMessageBox
)
from PyQt6.QtCore import Qt
from typing import Optional

from app.core.services.supplier_service import SupplierService
from app.core.models.supplier import Supplier, SupplierRating
from app.utils.database import SessionLocal


class SupplierInfoWidget(QWidget):
    """Widget d'informations complètes du fournisseur."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self._supplier_id = None
        self._supplier = None

        # Services
        self.db = SessionLocal()
        self.service = SupplierService(self.db)

        self._setup_ui()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()

    def _setup_ui(self):
        """Configure l'interface utilisateur avec le style unifié"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(12)

        # Message par défaut
        self.default_label = QLabel("Aucun fournisseur sélectionné")
        self.default_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.default_label.setStyleSheet("color: #666; font-style: italic; font-size: 14px;")
        main_layout.addWidget(self.default_label)

        # Style unifié inspiré de l'onglet réparation
        frame_style = "QFrame { border: none; background: transparent; }"
        title_style = "color: #555; font-weight: 600; font-size: 12px;"
        value_style = "color: #222; font-size: 14px; font-weight: 700;"

        def make_item(title, value_label_attr, word_wrap=False):
            w = QWidget()
            h = QHBoxLayout(w)
            h.setContentsMargins(6, 6, 6, 6)
            h.setSpacing(6)
            t = QLabel(title)
            t.setStyleSheet(title_style)
            t.setFixedWidth(120)
            t.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            h.addWidget(t)
            val = QLabel("-")
            val.setStyleSheet(value_style)
            val.setWordWrap(word_wrap)
            val.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            h.addWidget(val, 1)
            setattr(self, value_label_attr, val)
            return w

        # Frame 1 - Informations générales (colonne gauche)
        frame1 = QFrame()
        frame1.setStyleSheet(frame_style)
        f1 = QVBoxLayout(frame1)
        f1.setContentsMargins(8, 8, 8, 8)
        f1.setSpacing(8)
        f1.addWidget(make_item("ID", "id_label"))
        f1.addWidget(make_item("Nom", "name_label"))
        f1.addWidget(make_item("Statut", "status_label"))
        f1.addWidget(make_item("N° fiscal", "tax_id_label"))

        # Frame 2 - Contact (colonne centrale)
        frame2 = QFrame()
        frame2.setStyleSheet(frame_style)
        f2 = QVBoxLayout(frame2)
        f2.setContentsMargins(8, 8, 8, 8)
        f2.setSpacing(8)
        f2.addWidget(make_item("Contact", "contact_person_label"))
        f2.addWidget(make_item("Email", "email_label"))
        f2.addWidget(make_item("Téléphone", "phone_label"))
        f2.addWidget(make_item("Note", "rating_label"))

        # Frame 3 - Adresse et conditions (colonne droite)
        frame3 = QFrame()
        frame3.setStyleSheet(frame_style)
        f3 = QVBoxLayout(frame3)
        f3.setContentsMargins(8, 8, 8, 8)
        f3.setSpacing(8)
        f3.addWidget(make_item("Adresse", "address_label", word_wrap=True))
        f3.addWidget(make_item("Localité", "locality_label", word_wrap=True))
        f3.addWidget(make_item("Paiement", "payment_terms_label"))

        # Disposer les 3 frames côte à côte
        row_layout = QHBoxLayout()
        row_layout.setSpacing(12)
        row_layout.addWidget(frame1, 1)
        row_layout.addWidget(frame2, 1)
        row_layout.addWidget(frame3, 1)

        # Widget conteneur pour les colonnes
        self.content_widget = QWidget()
        self.content_widget.setLayout(row_layout)
        self.content_widget.hide()
        main_layout.addWidget(self.content_widget)

        # Ajouter un stretch pour éviter que les frames s'étirent trop
        main_layout.addStretch()



    def set_supplier_id(self, supplier_id: int):
        """Définit l'ID du fournisseur et charge ses informations."""
        print(f"SupplierInfoWidget: set_supplier_id appelé avec ID: {supplier_id}")
        self._supplier_id = supplier_id
        if supplier_id:
            # Test simple : afficher directement un message
            self.default_label.hide()
            self.content_widget.show()

            # Créer un fournisseur fictif pour tester l'affichage
            class FakeSupplier:
                def __init__(self):
                    self.id = supplier_id
                    self.name = f"Fournisseur Test #{supplier_id}"
                    self.email = "<EMAIL>"
                    self.phone = "0123456789"
                    self.address = "123 Rue Test"
                    self.commune = "Commune Test"
                    self.city = "Ville Test"
                    self.postal_code = "12345"
                    self.tax_id = "TAX123"
                    self.active = True
                    self.contact_person = "Contact Test"
                    self.payment_terms = "30 jours"
                    self.rating = None
                    self.evaluation_scores = {}

            self._supplier = FakeSupplier()
            self._display_supplier_info()

            # Aussi essayer de charger les vraies données
            self.load_supplier_info()
        else:
            self.clear()

    def clear(self):
        """Réinitialise l'affichage."""
        self._supplier_id = None
        self._supplier = None

        # Masquer le widget de contenu
        self.content_widget.hide()

        # Afficher le message par défaut
        self.default_label.show()

    def load_supplier_info(self):
        """Charge les informations du fournisseur"""
        print(f"SupplierInfoWidget: load_supplier_info appelé avec ID: {self._supplier_id}")
        if not self._supplier_id:
            print("SupplierInfoWidget: Aucun ID de fournisseur, arrêt")
            return

        try:
            print("SupplierInfoWidget: Chargement synchrone du fournisseur")
            # Chargement synchrone direct
            from app.core.models.supplier import Supplier
            self._supplier = self.db.query(Supplier).filter(Supplier.id == self._supplier_id).first()
            print(f"SupplierInfoWidget: Fournisseur récupéré: {self._supplier}")

            if self._supplier:
                print(f"SupplierInfoWidget: Affichage des informations pour: {self._supplier.name}")
                self._display_supplier_info()
            else:
                print("SupplierInfoWidget: Aucun fournisseur trouvé")
                self.clear()

        except Exception as e:
            print(f"Erreur lors du chargement des informations du fournisseur: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement: {str(e)}")



    def _display_supplier_info(self):
        """Affiche les informations du fournisseur avec le style unifié"""
        print(f"SupplierInfoWidget: _display_supplier_info appelé")
        if not self._supplier:
            print("SupplierInfoWidget: Aucun fournisseur à afficher")
            return

        print(f"SupplierInfoWidget: Affichage des informations pour {self._supplier.name}")
        # Masquer le message par défaut
        self.default_label.hide()

        # Remplir les informations générales (colonne gauche)
        self.id_label.setText(str(self._supplier.id))
        self.name_label.setText(self._supplier.name or "-")
        status_text = "✅ Actif" if self._supplier.active else "❌ Inactif"
        self.status_label.setText(status_text)
        self.tax_id_label.setText(self._supplier.tax_id or "-")

        # Remplir les informations de contact (colonne centrale)
        self.contact_person_label.setText(self._supplier.contact_person or "-")
        self.email_label.setText(self._supplier.email or "-")
        self.phone_label.setText(self._supplier.phone or "-")

        # Note d'évaluation
        rating_text = self._get_rating_text(self._supplier.rating)
        self.rating_label.setText(rating_text)

        # Remplir l'adresse et conditions (colonne droite)
        self.address_label.setText(self._supplier.address or "-")

        # Localité (commune, ville, code postal)
        address_parts = []
        if self._supplier.commune:
            address_parts.append(self._supplier.commune)
        if self._supplier.city:
            address_parts.append(self._supplier.city)
        if self._supplier.postal_code:
            address_parts.append(self._supplier.postal_code)

        locality_text = " - ".join(address_parts) if address_parts else "-"
        self.locality_label.setText(locality_text)

        # Conditions de paiement
        self.payment_terms_label.setText(self._supplier.payment_terms or "-")

        # Afficher le widget de contenu
        print("SupplierInfoWidget: Affichage du widget de contenu")
        self.content_widget.show()

        # Forcer la mise à jour de l'affichage
        self.update()
        self.repaint()
        print("SupplierInfoWidget: Widget de contenu affiché et mis à jour")



    def _get_rating_text(self, rating):
        """Convertit la note en texte lisible et compact"""
        if not rating:
            return "Non évalué"

        rating_texts = {
            SupplierRating.EXCELLENT: "⭐⭐⭐⭐⭐",
            SupplierRating.GOOD: "⭐⭐⭐⭐",
            SupplierRating.AVERAGE: "⭐⭐⭐",
            SupplierRating.FAIR: "⭐⭐",
            SupplierRating.POOR: "⭐"
        }
        return rating_texts.get(rating, "Non évalué")